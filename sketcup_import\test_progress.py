# -*- coding: utf-8 -*-
"""
اختبار نظام Progress Bar لاستيراد SketchUp
"""

import bpy

class TestProgressOperator(bpy.types.Operator):
    """اختبار نافذة التقدم"""
    bl_idname = "import_scene.test_progress"
    bl_label = "اختبار نافذة التقدم"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        # اختبار نافذة التقدم
        bpy.ops.import_scene.skp_progress('INVOKE_DEFAULT')
        
        self.report({'INFO'}, "تم فتح نافذة التقدم للاختبار")
        return {'FINISHED'}


def register():
    bpy.utils.register_class(TestProgressOperator)

def unregister():
    bpy.utils.unregister_class(TestProgressOperator)

if __name__ == "__main__":
    register()
