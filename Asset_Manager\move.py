import bpy
import os
from .utilities import *
from .base_operator_class import kh_BaseBulkOperator

class kh_OT_MoveOperator(kh_BaseBulkOperator):
    """Move Assets to a Catalog"""
    bl_idname = "asset.kh_bulk_asset_mover"
    bl_label = "Asset Mover"
    bl_options = {"REGISTER"}

    catalog: bpy.props.EnumProperty(
        name="Destination Catalog", items=kh_item_callback)

    def main(self, context):
        directory = kh_get_catalog_directory(context)
        for f in bpy.context.selected_assets:
            if f.local_id == None:
                path = f.full_library_path
                type_out = kh_id_type_to_type_name(f.id_type)
                if path not in self.commands.keys():
                    self.commands[path] = []
                self.commands[path].append(
                    "bpy.data."+type_out+"['"+f.name+"'].asset_data.catalog_id =\'"+self.catalog+"\';")
            else:
                f.local_id.asset_data.catalog_id = self.catalog

# def ASSET_MT_move_menu_func(self, context):
#     self.layout.operator_context = 'INVOKE_DEFAULT'
#     self.layout.operator(ASSET_OT_MoveOperator.bl_idname,text=ASSET_OT_MoveOperator.bl_label)
    
def kh_MT_move_menu_func(self, context):
    KH = context.preferences.addons['KH-Tools'].preferences.KH_Asset == True
    if KH :
        layout=self.layout
        row=layout.row(align=True)
        row.operator('asset.kh_bulk_asset_mover',text='Move',icon='ASSET_MANAGER')
