khModifiers = {
    'wall': {
        'label': 'Wall',
        'icon': 'AXIS_SIDE'
    },
    'array': {
        'label': 'Array',
        'icon': 'MOD_ARRAY'
    },
    'scatter': {
        'label': 'Scatter',
        'icon': 'OUTLINER_OB_CURVES'
    },
    'displace': {
        'label': 'Displace',
        'icon': 'MOD_DISPLACE'
    },
    'scatterpaint': {
        'label': 'Scatter Paint',
        'icon': 'OUTLINER_OB_CURVES'
    },
    'curvearray': {
        'label': 'Auto Array on Curve',
        'icon': 'CURVE_PATH'
    },
    'window': {
        'label': 'Window',
        'icon': 'WINDOW'
    },
    'group': {
        'label': 'Group',
        'icon': 'GROUP'
    },
    'instance': {
        'label': 'Instance',
        'icon': 'GROUP'
    },
    'pointeffector': {
        'label': 'Point Effector',
        'icon': 'PARTICLES'
    },
    'boolean': {
        'label': 'Boolean',
        'icon': 'MOD_BOOLEAN'
    },
    'boolean': {
        'label': 'Boolean',
        'icon': 'MOD_BOOLEAN'
    },
    'proxy': {
        'label': 'Proxy',
        'icon': 'MESH_ICOSPHERE'
    },
    'wallbrick': {
        'label': 'Wall Brick',
        'icon': 'MOD_BUILD'
    },
    'ivy': {
        'label': 'Ivy',
        'icon': 'FORCE_MAGNETIC'
    },
    'pointsnapinstance': {
        'label': 'Point Snap Instance',
        'icon': 'STYLUS_PRESSURE'
    },
    'grass': {
        'label': 'Grass',
        'icon': 'MATERIAL'
    },
    'plant': {
        'label': 'Plant',
        'icon': 'MATERIAL'
    },
    'rock': {
        'label': 'Rock',
        'icon': 'MATERIAL'
    },
    'tree': {
        'label': 'Tree',
        'icon': 'MATERIAL'
    },
    'stump': {
        'label': 'Stump',
        'icon': 'MATERIAL'
    },
    'instancesdisplace': {
        'label': 'Instances Displace',
        'icon': 'SEQ_LUMA_WAVEFORM'
    },
    'asset': {
        'label': 'Save as Asset',
        'icon': 'ASSET_MANAGER'
    },
    'material': {
        'label': 'Save Material',
        'icon': 'ASSET_MANAGER'
    },
    'pipes': {
        'label': 'Pipes',
        'icon': 'MOD_SIMPLEDEFORM'
    },
    'beamwire': {
        'label': 'Beam Wire',
        'icon': 'MOD_TRIANGULATE'
    },
    'linearstair': {
        'label': 'Stair Linear',
        'icon': 'IPO_CONSTANT'
    },
    'spiralstair': {
        'label': 'Stair Spiral',
        'icon': 'IPO_CONSTANT'
    },
    'beam': {
        'label': 'Beam',
        'icon': 'EVENT_H'
    },
    'floor': {
        'label': 'Floor',
        'icon': 'VIEW_PERSPECTIVE'
    },
    'handrail': {
        'label': 'Handrail',
        'icon': 'ALIGN_JUSTIFY'
    },
    'column': {
        'label': 'Column',
        'icon': 'CON_SAMEVOL'
    },
    'deform': {
        'label': 'Deform',
        'icon': 'MOD_SIMPLEDEFORM'
    },
    'camera': {
        'label': 'CamCulling',
        'icon': 'OUTLINER_OB_CAMERA'
    },
    'cable': {
        'label': 'Cable',
        'icon': 'OUTLINER_DATA_GREASEPENCIL'
    },
    'fence': {
        'label': 'Fence',
        'icon': 'PAUSE'
    },
    'siding': {
        'label': 'Siding',
        'icon': 'SORTSIZE'
    },
    'tiles': {
        'label': 'Tiles',
        'icon': 'PMARKER_ACT'
    },
}
