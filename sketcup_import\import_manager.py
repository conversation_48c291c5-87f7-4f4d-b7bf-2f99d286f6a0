# -*- coding: utf-8 -*-
"""
مدير استيراد SketchUp مع progress bar
"""

import bpy
import time
from bpy.props import StringProperty, FloatProperty

class SketchUpImportManager(bpy.types.Operator):
    """مدير استيراد SketchUp مع progress bar"""
    bl_idname = "import_scene.skp_manager"
    bl_label = "مدير استيراد SketchUp"
    bl_options = {'REGISTER', 'INTERNAL'}
    
    # خصائص التقدم
    progress: FloatProperty(
        name="التقدم",
        description="نسبة التقدم في الاستيراد",
        default=0.0,
        min=0.0,
        max=100.0,
        subtype='PERCENTAGE'
    )
    
    status_message: StringProperty(
        name="الحالة",
        description="رسالة الحالة الحالية",
        default="جاري التحضير للاستيراد..."
    )
    
    filepath: StringProperty(
        name="مسار الملف",
        description="مسار ملف SketchUp",
        default=""
    )
    
    # متغيرات التحكم
    is_importing = False
    import_finished = False
    import_error = False
    error_message = ""
    
    def __init__(self):
        self.progress = 0.0
        self.status_message = "جاري التحضير للاستيراد..."
        self.is_importing = False
        self.import_finished = False
        self.import_error = False
        self.error_message = ""
        self.timer = None
    
    def modal(self, context, event):
        """معالج الأحداث للنافذة المودال"""
        
        if event.type == 'TIMER':
            # تحديث النافذة
            context.area.tag_redraw()
            
            # التحقق من انتهاء الاستيراد
            if self.import_finished:
                if self.timer:
                    context.window_manager.event_timer_remove(self.timer)
                
                if self.import_error:
                    self.report({'ERROR'}, f"خطأ في الاستيراد: {self.error_message}")
                    return {'CANCELLED'}
                else:
                    self.report({'INFO'}, "تم استيراد الملف بنجاح")
                    return {'FINISHED'}
        
        # السماح بالإلغاء بـ ESC
        if event.type == 'ESC':
            if self.timer:
                context.window_manager.event_timer_remove(self.timer)
            self.report({'INFO'}, "تم إلغاء الاستيراد")
            return {'CANCELLED'}
        
        return {'PASS_THROUGH'}
    
    def invoke(self, context, event):
        """بدء النافذة المودال"""

        # الحصول على مسار الملف من window manager
        self.filepath = context.window_manager.skp_import_filepath

        # إعداد النافذة
        wm = context.window_manager
        self.timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        # بدء الاستيراد
        self.start_import(context)

        return {'RUNNING_MODAL'}
    
    def draw(self, context):
        """رسم واجهة النافذة"""
        layout = self.layout
        
        # عنوان النافذة
        layout.label(text="استيراد ملف SketchUp", icon='IMPORT')
        layout.separator()
        
        # شريط التقدم
        col = layout.column()
        col.prop(self, "progress", text="التقدم", slider=True)
        
        # رسالة الحالة
        col.label(text=f"الحالة: {self.status_message}")
        
        # معلومات إضافية
        if not self.import_finished:
            box = col.box()
            box.label(text="معلومات:")
            box.label(text="• اضغط ESC للإلغاء")
            box.label(text="• تحقق من Console للتفاصيل")
        
        layout.separator()
    
    def start_import(self, context):
        """بدء عملية الاستيراد"""
        
        self.is_importing = True
        self.update_progress(5, "جاري بدء الاستيراد...")
        
        try:
            # إنشاء دالة callback لتحديث التقدم
            def progress_callback(progress, message):
                self.update_progress(progress, message)
            
            # تشغيل الاستيراد
            from . import SceneImporter
            importer = SceneImporter()
            importer.set_filename(self.filepath)
            importer.set_progress_callback(progress_callback)
            
            # تحديد الخيارات الافتراضية
            options = {
                'reuse_material': True,
                'reuse_existing_groups': False,
                'max_instance': 1000,
                'render_engine': 'CYCLES',
                'import_camera': False,
                'scenes_as_camera': False,
                'import_scene': '',
                'dedub_only': False,
                'dedub_type': 'FACE'
            }
            
            result = importer.load(context, **options)
            
            if result == {'FINISHED'}:
                self.update_progress(100, "تم الاستيراد بنجاح!")
                self.import_finished = True
            else:
                self.import_error = True
                self.error_message = "فشل في الاستيراد"
                self.import_finished = True
                
        except Exception as e:
            import traceback
            self.import_error = True
            self.error_message = str(e)
            self.import_finished = True
            
            # حفظ رسالة الخطأ للنسخ
            error_msg = f"خطأ في الاستيراد:\n{str(e)}\n\nتفاصيل:\n{traceback.format_exc()}"
            context.window_manager.skp_error_message = error_msg
    
    def update_progress(self, progress, message):
        """تحديث التقدم والرسالة"""
        self.progress = progress
        self.status_message = message
        print(f"التقدم: {progress}% - {message}")


def start_import_with_progress(filepath, context):
    """بدء الاستيراد مع نافذة التقدم"""
    
    # إنشاء مدير الاستيراد
    manager = SketchUpImportManager()
    manager.filepath = filepath
    
    # بدء النافذة المودال
    return bpy.ops.import_scene.skp_manager('INVOKE_DEFAULT')


# تسجيل الكلاسات
classes = (
    SketchUpImportManager,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in classes:
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
