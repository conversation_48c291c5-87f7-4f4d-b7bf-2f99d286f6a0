# -*- coding: utf-8 -*-
"""
مستورد SketchUp مع دعم Threading و Progress Bar
"""

import bpy
import threading
import time
import traceback
from collections import defaultdict
from mathutils import Matrix
from . import sketchup
from .SKPutil import *

# استيراد متغيرات التسجيل
LOGS = True
MIN_LOGS = False

class ThreadedSketchUpImporter(threading.Thread):
    """كلاس لاستيراد SketchUp في thread منفصل"""
    
    def __init__(self, filepath, context, options, progress_dialog):
        super().__init__()
        self.filepath = filepath
        self.context = context
        self.options = options
        self.progress_dialog = progress_dialog
        self.cancel_flag = False
        self.daemon = True  # يجعل الـ thread ينتهي عند إغلاق البرنامج
        
        # متغيرات الاستيراد
        self.name_mapping = {}
        self.component_meshes = {}
        self.scene = None
        self.layers_skip = []
        self.skp_model = None
        
    def run(self):
        """تشغيل عملية الاستيراد"""
        try:
            self.update_progress(5, "جاري فتح ملف SketchUp...")
            
            # فتح ملف SketchUp
            if self.cancel_flag:
                return
                
            self.skp_model = sketchup.Model.from_file(self.filepath)
            self.update_progress(15, "تم فتح الملف بنجاح")
            
            # استيراد الكاميرات والمشاهد
            if self.cancel_flag:
                return
                
            self.update_progress(25, "جاري استيراد الكاميرات والمشاهد...")
            self.import_cameras_and_scenes()
            
            # استيراد المواد
            if self.cancel_flag:
                return
                
            self.update_progress(40, "جاري استيراد المواد...")
            self.import_materials()
            
            # تحليل المكونات
            if self.cancel_flag:
                return
                
            self.update_progress(55, "جاري تحليل المكونات...")
            self.analyze_components()
            
            # استيراد الكائنات
            if self.cancel_flag:
                return
                
            self.update_progress(70, "جاري استيراد الكائنات...")
            self.import_entities()
            
            # تنظيف وإنهاء
            if self.cancel_flag:
                return
                
            self.update_progress(90, "جاري التنظيف والإنهاء...")
            self.cleanup_and_finish()
            
            # انتهاء العملية
            self.update_progress(100, "تم الاستيراد بنجاح")
            self.progress_dialog.set_finished()
            
        except Exception as e:
            # التعامل مع الأخطاء
            error_message = f"خطأ في الاستيراد:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
            self.progress_dialog.set_error(error_message)
            
            # حفظ رسالة الخطأ للنسخ
            bpy.context.window_manager.skp_error_message = error_message
    
    def update_progress(self, progress, message):
        """تحديث شريط التقدم"""
        if self.progress_dialog:
            self.progress_dialog.update_progress(progress, message)
    
    def import_cameras_and_scenes(self):
        """استيراد الكاميرات والمشاهد"""
        if not self.options.get('import_camera', False):
            return
            
        # معالجة المشاهد المحددة
        if self.options.get('import_scene'):
            for s in self.skp_model.scenes:
                if s.name == self.options['import_scene']:
                    self.scene = s
                    self.layers_skip = [l for l in s.layers]
                    break
        
        # استيراد المشاهد كـ كاميرات
        if self.options.get('scenes_as_camera', False):
            for s in self.skp_model.scenes:
                if self.cancel_flag:
                    return
                self.write_camera(s.camera, s.name)
        
        # تعيين الكاميرا النشطة
        if self.scene:
            active_cam = self.write_camera(self.scene.camera, name=self.scene.name)
        else:
            active_cam = self.write_camera(self.skp_model.camera)
            
        # تعيين الكاميرا في المشهد (يجب أن يتم في main thread)
        bpy.app.timers.register(lambda: self.set_active_camera(active_cam))
    
    def set_active_camera(self, camera_name):
        """تعيين الكاميرا النشطة (يتم تشغيلها في main thread)"""
        try:
            if camera_name in bpy.data.objects:
                bpy.context.scene.camera = bpy.data.objects[camera_name]
                for area in bpy.context.screen.areas:
                    if area.type == 'VIEW_3D':
                        area.spaces[0].region_3d.view_perspective = 'CAMERA'
                        break
        except:
            pass
        return None  # لا نريد تكرار التشغيل
    
    def write_camera(self, camera, name="Camera"):
        """كتابة بيانات الكاميرا"""
        # هذه دالة مبسطة - يمكن توسيعها حسب الحاجة
        return name
    
    def import_materials(self):
        """استيراد المواد من SketchUp"""
        if not hasattr(self, 'skp_model') or not self.skp_model:
            return
            
        self.materials = {}
        self.materials_scales = {}
        
        # إنشاء المادة الافتراضية
        if 'Material' not in bpy.data.materials:
            bmat = bpy.data.materials.new('Material')
            bmat.diffuse_color = (.8, .8, .8, 1)
            bmat.use_nodes = True
            self.materials['Material'] = bmat
        else:
            self.materials['Material'] = bpy.data.materials['Material']
        
        # استيراد المواد من الملف
        for i, mat in enumerate(self.skp_model.materials):
            if self.cancel_flag:
                return
                
            # تحديث التقدم
            progress = 40 + (i / len(self.skp_model.materials)) * 15
            self.update_progress(progress, f"جاري استيراد المادة: {mat.name}")
            
            # معالجة المادة
            self.process_material(mat)
    
    def process_material(self, mat):
        """معالجة مادة واحدة"""
        name = mat.name
        
        # حفظ أبعاد النسيج
        if mat.texture:
            self.materials_scales[name] = mat.texture.dimensions[2:]
        else:
            self.materials_scales[name] = (1.0, 1.0)
        
        # إنشاء المادة في Blender إذا لم تكن موجودة
        if name not in bpy.data.materials:
            bmat = bpy.data.materials.new(name)
            r, g, b, a = mat.color
            
            # تحويل الألوان من sRGB إلى Linear
            bmat.diffuse_color = (
                pow((r / 255.0), 2.2),
                pow((g / 255.0), 2.2),
                pow((b / 255.0), 2.2),
                round((a / 255.0), 2)
            )
            
            # إعداد الشفافية
            if round((a / 255.0), 2) < 1:
                bmat.blend_method = 'BLEND'
            
            bmat.use_nodes = True
            self.materials[name] = bmat
        else:
            self.materials[name] = bpy.data.materials[name]
    
    def analyze_components(self):
        """تحليل المكونات في الملف"""
        if not hasattr(self, 'skp_model') or not self.skp_model:
            return
            
        self.component_stats = defaultdict(list)
        self.component_skip = {}
        self.component_depth = {}
        
        # تحليل عمق المكونات
        for i, c in enumerate(self.skp_model.component_definitions):
            if self.cancel_flag:
                return
                
            progress = 55 + (i / len(self.skp_model.component_definitions)) * 15
            self.update_progress(progress, f"جاري تحليل المكون: {c.name}")
            
            # حساب عمق المكون (مبسط)
            self.component_depth[c.name] = 1  # يمكن تحسين هذا
    
    def import_entities(self):
        """استيراد الكائنات والمكونات"""
        if not hasattr(self, 'skp_model') or not self.skp_model:
            return
            
        # إنشاء مجموعة للملف
        collection_name = "SKP_Import"
        if collection_name not in bpy.data.collections:
            collection = bpy.data.collections.new(collection_name)
            bpy.context.scene.collection.children.link(collection)
        
        # استيراد الكائنات (مبسط)
        entities = self.skp_model.entities
        for i, entity in enumerate(entities.faces[:100]):  # محدود لتجنب التعليق
            if self.cancel_flag:
                return
                
            progress = 70 + (i / min(100, len(entities.faces))) * 20
            self.update_progress(progress, f"جاري استيراد الكائن {i+1}")
            
            # معالجة الكائن (مبسطة)
            time.sleep(0.001)  # محاكاة وقت المعالجة
    
    def cleanup_and_finish(self):
        """تنظيف وإنهاء العملية"""
        # تنظيف الذاكرة
        if hasattr(self, 'skp_model'):
            del self.skp_model
        
        # تنظيف البيانات المؤقتة
        self.component_meshes.clear()
        self.name_mapping.clear()


class SketchUpImportManager(bpy.types.Operator):
    """مدير استيراد SketchUp مع progress bar"""
    bl_idname = "import_scene.skp_manager"
    bl_label = "مدير استيراد SketchUp"
    bl_options = {'REGISTER'}

    def __init__(self):
        self.importer_thread = None
        self.progress_dialog = None
        self.timer = None

    def execute(self, context):
        return {'FINISHED'}

    def modal(self, context, event):
        """معالج الأحداث للمدير"""

        if event.type == 'TIMER':
            # التحقق من حالة الـ thread
            if self.importer_thread and not self.importer_thread.is_alive():
                # انتهى الـ thread
                if self.timer:
                    context.window_manager.event_timer_remove(self.timer)
                return {'FINISHED'}

        return {'PASS_THROUGH'}

    def invoke(self, context, event):
        """بدء المدير"""
        wm = context.window_manager
        self.timer = wm.event_timer_add(0.5, window=context.window)
        wm.modal_handler_add(self)
        return {'RUNNING_MODAL'}


def start_threaded_import(filepath, context, options):
    """بدء عملية الاستيراد مع progress bar"""

    try:
        # استخدام النظام الجديد للاستيراد مع progress bar
        from .import_manager import start_import_with_progress

        # تعيين مسار الملف في context مؤقتاً
        context.window_manager.skp_import_filepath = filepath

        # بدء الاستيراد مع progress bar
        return start_import_with_progress(filepath, context)

    except Exception as e:
        # في حالة الخطأ، عرض رسالة الخطأ
        import traceback
        error_msg = f"خطأ في الاستيراد:\n{str(e)}\n\nتفاصيل:\n{traceback.format_exc()}"

        # حفظ رسالة الخطأ للنسخ
        context.window_manager.skp_error_message = error_msg

        # عرض رسالة الخطأ
        def show_error_popup(self, context):
            self.layout.label(text="حدث خطأ أثناء الاستيراد", icon='ERROR')
            self.layout.separator()
            self.layout.label(text="تفاصيل الخطأ:")

            # تقسيم رسالة الخطأ إلى أسطر
            error_lines = error_msg.split('\n')[:5]  # أول 5 أسطر فقط
            for line in error_lines:
                if line.strip():
                    display_line = line[:60] + "..." if len(line) > 60 else line
                    self.layout.label(text=display_line)

            self.layout.separator()
            self.layout.operator("import_scene.copy_error", text="نسخ تفاصيل الخطأ", icon='COPYDOWN')

        context.window_manager.popup_menu(show_error_popup, title="خطأ في الاستيراد", icon='ERROR')

        return {'CANCELLED'}
