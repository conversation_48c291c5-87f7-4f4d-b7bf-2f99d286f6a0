from sqlite3 import Row
import bpy

import bpy, os

def add_nod():
    script_dir = os.path.dirname(os.path.realpath(__file__))
    folder_path = os.path.join(script_dir, "asset")
    file_name = "KH-Post Group.blend"
    world_file_path = os.path.join(folder_path, file_name)

    with bpy.data.libraries.load(world_file_path, link=False) as (data_from, data_to):
        data_to.node_groups = [name for name in data_from.node_groups if name.startswith("KH-Post Group")]
    node_group_name = "KH-Post Group"
    if node_group_name in bpy.data.node_groups:
        # إضافة Node Group إلى نافذة الـ Compositing
        bpy.context.scene.use_nodes = True
        tree = bpy.context.scene.node_tree
        nodes = tree.nodes
        node_group = bpy.data.node_groups[node_group_name]
        # تحديد موقع الـ Node Group في نافذة الـ Compositing
        for node in nodes:
            if isinstance(node, bpy.types.CompositorNodeGroup):
                nodes.remove(node)

        group_node = nodes.new('CompositorNodeGroup')
        group_node.node_tree = node_group
        group_node.location = (-200, 0)

        # ربط الـ Node Group بالـ Composite Node
        composite_node = None
        for node in nodes:
            if node.type == 'COMPOSITE':
                composite_node = node
                break

        # if composite_node:
        #     links = tree.links
        #     link = links.new(group_node.outputs[0], composite_node.inputs[0])
    

    
    # exposure = nodes.new('CompositorNodeExposure')
    # exposure.label = "KH-Post Exposure"
    # hue_sat = nodes.new('CompositorNodeHueSat')
    # hue_sat.label = "KH-Post HueSat"
    # bright_contrast = nodes.new('CompositorNodeBrightContrast')
    # bright_contrast.label = "KH-Post BrightContrast"
    # gamma = nodes.new('CompositorNodeGamma')
    # gamma.label = "KH-Post Gamma"
    glare = nodes.new('CompositorNodeGlare')
    glare.label = "KH-Post FOG_GLOW"
    glare.glare_type = 'FOG_GLOW'
    glare.quality = 'HIGH'
    glare.mix = -0.8
    glare.mute = True

    glare1 = nodes.new('CompositorNodeGlare')
    glare1.label = "KH-Post SIMPLE_STAR"
    glare1.glare_type = 'SIMPLE_STAR'
    glare1.quality = 'HIGH'
    glare1.mix = -0.8
    glare1.iterations = 2
    glare1.mute = True
    
    group_node.location = (800, 0)
    glare.location = (1000, 0)
    glare1.location = (1200, 0)


#Add post_production
class Post_Production(bpy.types.Operator):
    bl_idname = "object.post_production"
    bl_label = "Post Production"

    def execute(self, context):
        bpy.context.scene.use_nodes = True
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            links = bpy.context.scene.node_tree.links 
            links.new(r_layers.outputs['Image'], composite.inputs['Image']) 
        else:
            composite = nodes['Composite']
        
        target_node_name = "KH-Post Group"
        compositor = bpy.context.scene.node_tree
        target_node = None
        for node in compositor.nodes:
            if node.label == target_node_name:
                target_node = node
                break
        if target_node is None:
            
            node_group_name = "KH-Post Group"
            if not node_group_name in bpy.data.node_groups:
                script_dir = os.path.dirname(os.path.realpath(__file__))
                folder_path = os.path.join(script_dir, "asset")
                file_name = "KH-Post Group.blend"
                world_file_path = os.path.join(folder_path, file_name)

                with bpy.data.libraries.load(world_file_path, link=False) as (data_from, data_to):
                    data_to.node_groups = [name for name in data_from.node_groups if name.startswith("KH-Post Group")]
            
            if node_group_name in bpy.data.node_groups:
                tree = bpy.context.scene.node_tree
                nodes = tree.nodes
                node_group = bpy.data.node_groups[node_group_name]
                group_node = nodes.new('CompositorNodeGroup')
                group_node.node_tree = node_group
                group_node.location = (-200, 0)
                group_node.label = "KH-Post Group"

            glare = nodes.new('CompositorNodeGlare')
            glare.label = "KH-Post FOG_GLOW"
            glare.glare_type = 'FOG_GLOW'
            glare.quality = 'HIGH'
            glare.mix = -0.8
            glare.mute = True

            glare1 = nodes.new('CompositorNodeGlare')
            glare1.label = "KH-Post SIMPLE_STAR"
            glare1.glare_type = 'SIMPLE_STAR'
            glare1.quality = 'HIGH'
            glare1.mix = -0.8
            glare1.iterations = 2
            glare1.mute = True
            
            group_node.location = (800, 0)
            glare.location = (1000, 0)
            glare1.location = (1200, 0)

            switch_label = "KH-Post Group"
            switch_node = None
            for node in bpy.context.scene.node_tree.nodes:
                if node.label == switch_label:
                    switch_node = node
                    break

            if switch_node:
                composite_node = bpy.context.scene.node_tree.nodes.get('Composite')
                image_input = composite_node.inputs['Image']
                connected_node = None
                for link in bpy.context.scene.node_tree.links:
                    if link.to_node == composite_node and link.to_socket == image_input:
                        connected_node = link.from_node
                        break
                if connected_node:
                    switch_input = switch_node.inputs['Image']
                    bpy.context.scene.node_tree.links.new(connected_node.outputs[0], switch_input)
                
            links = bpy.context.scene.node_tree.links
            links.new(group_node.outputs['Image'], glare.inputs['Image'])
            links.new(glare.outputs['Image'], glare1.inputs['Image'])
            links.new(glare1.outputs['Image'], composite.inputs['Image'])
            
            Viewer = bpy.context.scene.node_tree.nodes.get("Viewer")
            if Viewer:
                Viewer.location = (1400, -150)
                links.new(Viewer.inputs[0], glare1.outputs[0])
            
            composite.location = (1400, 0)
            
        else:
                     
            group_node = nodes[node.label == "KH-Post Group"]
            glare =  nodes[node.label == "KH-Post FOG_GLOW"]
                  
            switch_label = "KH-Post Group"
            switch_node = None
            for node in bpy.context.scene.node_tree.nodes:
                if node.label == switch_label:
                    switch_node = node
                    break
            
            glare1_label = "KH-Post SIMPLE_STAR"
            glare1 = None
            for node in bpy.context.scene.node_tree.nodes:
                if node.label == glare1_label:
                    glare1= node
                    break

            if switch_node:
                composite_node = bpy.context.scene.node_tree.nodes.get('Composite')
                image_input = composite_node.inputs['Image']
                connected_node = None
                for link in bpy.context.scene.node_tree.links:
                    if link.to_node == composite_node and link.to_socket == image_input:
                        connected_node = link.from_node
                        break
                if connected_node:
                    switch_input = switch_node.inputs['Image']
                    bpy.context.scene.node_tree.links.new(connected_node.outputs[0], switch_input)
            
            links = bpy.context.scene.node_tree.links   
            if 'Image' in glare1.outputs and 'Image' in composite.inputs:
                links.new(glare1.outputs['Image'], composite.inputs['Image'])

            Viewer = bpy.context.scene.node_tree.nodes.get("Viewer")
            if Viewer:
                Viewer.location = (1400, -150)
                links.new(Viewer.inputs[0], glare1.outputs[0])

        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                bpy.context.window.scene = bpy.data.scenes[0]
                area.spaces[0].shading.use_compositor = 'ALWAYS'  
        self.report({'INFO'}, 'Post Production is activated')
        return {'FINISHED'}

#ClearCompositor///////////////////////////////////////////////////////////////////////////////////////////////////
    
class Clear_Compositor_Operator(bpy.types.Operator):
    bl_idname = "render.clear_compositor"
    bl_label = "Clear Compositor"

    def execute(self, context):
        # Get a reference to the current Compositor tree
        tree = bpy.context.scene.node_tree
        # Enable use nodes in Compositor
        bpy.context.scene.use_nodes = True
        # Check if RLayers and Composite nodes exist
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
            r_layers.location = (-100, 0)
        
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (1400, 0)
        else:
            composite = nodes['Composite']

        switch_name = "KH-Post Group"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.label == switch_name:
                switch_node = node
                break
        if switch_node:
            connected_node = None
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == switch_node and link.to_socket.name == 'Image':
                    connected_node = link.from_node
                    break
            if connected_node:
                composite_node = bpy.context.scene.node_tree.nodes.get("Composite")
                if composite_node:
                    bpy.context.scene.node_tree.links.new(composite_node.inputs['Image'], connected_node.outputs[0])

                Viewer = bpy.context.scene.node_tree.nodes.get("Viewer")
                if Viewer:
                    bpy.context.scene.node_tree.links.new(Viewer.inputs['Image'], connected_node.outputs[0])

        
        switch_name = "KH-Post Group"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.label == switch_name:
                switch_node = node
                break

        if switch_node:
            connected_nodes = []
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == switch_node and link.to_socket.name == 'Image':
                    connected_node = link.from_node
                    connected_nodes.append(connected_node)
                    bpy.context.scene.node_tree.links.remove(link)

        self.report({'INFO'}, 'Rest Post Production')
        
        # Get a reference to the current Compositor tree
        tree = bpy.context.scene.node_tree
        #bpy.context.space_data.shading.use_compositor = 'DISABLED'
        bpy.context.scene.use_nodes = True

        '''target_label = "KH-Post"
        compositor_window = bpy.context.scene.node_tree
        for node in compositor_window.nodes:
            if node.label.startswith(target_label):
                compositor_window.nodes.remove(node)'''
                  
        # Delete all existing nodes
        #for node in tree.nodes:
           # tree.nodes.remove(node)
        return {'FINISHED'} 
    

#rest///////////////////////////////////////////////////////////////////////////////
class Rest_Composito_rOperator(bpy.types.Operator):
    bl_idname = "render.rest_compositor"
    bl_label = "Rest Compositor"

    def execute(self, context):
        tree = bpy.context.scene.node_tree
        bpy.context.scene.use_nodes = True
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
            r_layers.location = (-100, 0)
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (1400, 0)
        else:
            composite = nodes['Composite']

        switch_name = "KH-Post Group"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.label == switch_name:
                switch_node = node
                break
        if switch_node:
            connected_node = None
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == switch_node and link.to_socket.name == 'Image':
                    connected_node = link.from_node
                    break
            if connected_node:
                composite_node = bpy.context.scene.node_tree.nodes.get("Composite")
                if composite_node:
                    bpy.context.scene.node_tree.links.new(composite_node.inputs['Image'], connected_node.outputs[0])

        self.report({'INFO'}, 'Rest Post Production')
                

        target_label = "KH-Post"
        compositor_window = bpy.context.scene.node_tree
        for node in compositor_window.nodes:
            if node.label.startswith(target_label):
                compositor_window.nodes.remove(node)
                  
        # Delete all existing nodes
        #for node in tree.nodes:
            #tree.nodes.remove(node)
              
       # /////////////////////////////////////////////////////////////////////////
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            links = bpy.context.scene.node_tree.links 
            links.new(r_layers.outputs['Image'], composite.inputs['Image']) 
        else:
            composite = nodes['Composite']
        
        target_node_name = "KH-Post Group"
        compositor = bpy.context.scene.node_tree
        target_node = None
        for node in compositor.nodes:
            if node.label == target_node_name:
                target_node = node
                break
        if target_node is None:
            node_group_name = "KH-Post Group"
            if not node_group_name in bpy.data.node_groups:
                script_dir = os.path.dirname(os.path.realpath(__file__))
                folder_path = os.path.join(script_dir, "asset")
                file_name = "KH-Post Group.blend"
                world_file_path = os.path.join(folder_path, file_name)

                with bpy.data.libraries.load(world_file_path, link=False) as (data_from, data_to):
                    data_to.node_groups = [name for name in data_from.node_groups if name.startswith("KH-Post Group")]
            
            if node_group_name in bpy.data.node_groups:
                tree = bpy.context.scene.node_tree
                nodes = tree.nodes
                node_group = bpy.data.node_groups[node_group_name]
                group_node = nodes.new('CompositorNodeGroup')
                group_node.node_tree = node_group
                group_node.location = (-200, 0)
                group_node.label = "KH-Post Group"

            glare = nodes.new('CompositorNodeGlare')
            glare.label = "KH-Post FOG_GLOW"
            glare.glare_type = 'FOG_GLOW'
            glare.quality = 'HIGH'
            glare.mix = -0.8
            glare.mute = True

            glare1 = nodes.new('CompositorNodeGlare')
            glare1.label = "KH-Post SIMPLE_STAR"
            glare1.glare_type = 'SIMPLE_STAR'
            glare1.quality = 'HIGH'
            glare1.mix = -0.8
            glare1.iterations = 2
            glare1.mute = True
            
            group_node.location = (800, 0)
            glare.location = (1000, 0)
            glare1.location = (1200, 0)

            switch_label = "KH-Post Group"
            switch_node = None
            for node in bpy.context.scene.node_tree.nodes:
                if node.label == switch_label:
                    switch_node = node
                    break

            if switch_node:
                composite_node = bpy.context.scene.node_tree.nodes.get('Composite')
                image_input = composite_node.inputs['Image']
                connected_node = None
                for link in bpy.context.scene.node_tree.links:
                    if link.to_node == composite_node and link.to_socket == image_input:
                        connected_node = link.from_node
                        break
                if connected_node:
                    switch_input = switch_node.inputs['Image']
                    bpy.context.scene.node_tree.links.new(connected_node.outputs[0], switch_input)
                
  
            links = bpy.context.scene.node_tree.links
            links.new(group_node.outputs['Image'], glare.inputs['Image'])
            links.new(glare.outputs['Image'], glare1.inputs['Image'])
            links.new(glare1.outputs['Image'], composite.inputs['Image'])
            
            Viewer = bpy.context.scene.node_tree.nodes.get("Viewer")
            if Viewer:
                Viewer.location = (1400, -150)
                links.new(Viewer.inputs[0], glare1.outputs[0])
            composite.location = (1400, 0)

        #bpy.context.space_data.shading.use_compositor = 'ALWAYS'
        
        bpy.context.scene.view_settings.use_curve_mapping = False

        self.report({'INFO'}, 'Rest Post Production')
        return {'FINISHED'}

#Delete_Compositor////////////////////////////////////////////////////////////////////////////////////////////
class Delete_Compositor_Operator(bpy.types.Operator):
    bl_idname = "render.delete_compositor"
    bl_label = "Delete Compositor"

    def execute(self, context):
        # Get a reference to the current Compositor tree
        tree = bpy.context.scene.node_tree
        # Enable use nodes in Compositor
        bpy.context.scene.use_nodes = True
        # Check if RLayers and Composite nodes exist
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
            r_layers.location = (-100, 0)
        
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (1400, 0)
        else:
            composite = nodes['Composite']

        switch_name = "KH-Post Group"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.label == switch_name:
                switch_node = node
                break
        if switch_node:
            connected_node = None
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == switch_node and link.to_socket.name == 'Image':
                    connected_node = link.from_node
                    break
            if connected_node:
                composite_node = bpy.context.scene.node_tree.nodes.get("Composite")
                if composite_node:
                    bpy.context.scene.node_tree.links.new(composite_node.inputs['Image'], connected_node.outputs[0])

                Viewer = bpy.context.scene.node_tree.nodes.get("Viewer")
                if Viewer:
                    bpy.context.scene.node_tree.links.new(Viewer.inputs['Image'], connected_node.outputs[0])
               

        self.report({'INFO'}, 'Post Production deleted')
        
        # Get a reference to the current Compositor tree
        tree = bpy.context.scene.node_tree
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                bpy.context.window.scene = bpy.data.scenes[0]
                area.spaces[0].shading.use_compositor = 'DISABLED' 
        #bpy.context.space_data.shading.use_compositor = 'DISABLED'
        bpy.context.scene.use_nodes = True

        target_label = "KH-Post"
        compositor_window = bpy.context.scene.node_tree
        for node in compositor_window.nodes:
            if node.label.startswith(target_label):
                compositor_window.nodes.remove(node)

        bpy.context.scene.view_settings.use_curve_mapping = False
         
        # Delete all existing nodes
        #for node in tree.nodes:
           # tree.nodes.remove(node)
        return {'FINISHED'}
    
#Activate Compositing //////////////////////////////////////////////////////////////////////////////////////////
class CompositorOperator(bpy.types.Operator):
    bl_idname = "object.compositor"
    bl_label = "Activate Compositor"

    def execute(self, context):
        target_workspace = "Compositing"
        if target_workspace in bpy.data.workspaces:
            main_window = bpy.context.window_manager.windows[0]
            main_window.workspace = bpy.data.workspaces[target_workspace]
        else :
             bpy.ops.wm.window_new()
             window = bpy.context.window_manager.windows[-1]
             bpy.context.area.ui_type = 'CompositorNodeTree'
        return {'FINISHED'}
    
# Switch.check = True ////////////////////////////////////////////////////////////////////////////////////////////
class Switch_On_Operator(bpy.types.Operator):
    bl_idname = "render.switch_on"
    bl_label = "Switch ON"
    
    def execute(self, context):
        compositor_window = bpy.context.scene.node_tree
        # ابحث عن النود بالاسم "Denoise Light Mix"
        Switch = None
        for node in compositor_window.nodes:
            if node.label == "Switch Light Mix":
                Switch = node
                break
        if Switch is not None:
            Switch.check = True       
        return {'FINISHED'}

    
class Switch_Off_Operator(bpy.types.Operator):
    bl_idname = "render.switch_off"
    bl_label = "Switch OFF"
    def execute(self, context):
        compositor_window = bpy.context.scene.node_tree
        # ابحث عن النود بالاسم "Denoise Light Mix"
        Switch = None
        for node in compositor_window.nodes:
            if node.label == "Switch Light Mix":
                Switch = node
                break
        if Switch is not None:
            Switch.check = False 
        return {'FINISHED'}
    
# VIEW_3D' 'ALWAYS' ////////////////////////////////////////////////////////////////////////////////////////////
class VIEW_3D_ALWAYS_Operator(bpy.types.Operator):
    bl_idname = "render.view_always"
    bl_label = "Show in view port"
    
    def execute(self, context):
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                bpy.context.window.scene = bpy.data.scenes[0] 
                area.spaces[0].shading.use_compositor = 'ALWAYS'
                break
            return {'FINISHED'}

    
class VIEW_3D_DISABLED_Operator(bpy.types.Operator):
    bl_idname = "render.view_disabled"
    bl_label = "Show in viewport"
    def execute(self, context):
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                bpy.context.window.scene = bpy.data.scenes[0] 
                area.spaces[0].shading.use_compositor = 'DISABLED'
                break
            return {'FINISHED'}
       
# Post Production PROPERTIES////////////////////////////////////////////////////////////////////
class Post_Production_Panel(bpy.types.Panel):
    bl_idname = "OBJECT_PT_post_product_panel"
    bl_label = "Post Production"
    bl_space_type  = "PROPERTIES"
    bl_region_type = "WINDOW"
    bl_context     = "render"
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        if 'KH-Tools' in context.preferences.addons:
            KH = context.preferences.addons['KH-Tools'].preferences.Light_Mix == True
            return KH
        else:
            return True

    def draw_header(self, context: bpy.types.Context):
        try:
            self.layout.label(
                text="", icon='SHADERFX')
        except KeyError:
            pass
        
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        if not bpy.context.scene.use_nodes:
            layout = self.layout
            box = layout.box()
            row = box.row(align=True)           
            row.operator("object.post_production", text="Activate")
        else:
            compositor_window = bpy.context.scene.node_tree
            Switch = None
            for node in compositor_window.nodes:
                if node.label == "Switch Light Mix":
                    Switch = node
                    break
            if Switch is not None and Switch.check == True :
                    box = layout.box()
                    row = box.row(align=True)
                    row.operator("render.switch_off", text="Fixt Black Screen", icon='ERROR') 
            else:
                target_node_name = "KH-Post Group"
                target_node = None
                for node1 in bpy.context.scene.node_tree.nodes:
                    if node1.label == target_node_name:
                        target_node = node
                        break
                if target_node is None:
                    box = layout.box()
                    row = box.row(align=True)
                    row .operator("object.post_production", text="Activate", icon='QUIT')
                else:
                    box = layout.box()
                    row = box.row(align=True)
                    scene = bpy.context.scene
                    node_tree = scene.node_tree
                    
                    glare_node = None
                    for node in node_tree.nodes:
                        if node.label == "KH-Post SIMPLE_STAR":
                            glare_node = node
                            break
                    
                    Group_node = None
                    for node in node_tree.nodes:
                        if node.label == "KH-Post Group":
                            Group_node = node
                            break
                        
                    glare1_node = None
                    for node in node_tree.nodes:
                        if node.label == "KH-Post FOG_GLOW":
                            glare1_node = node
                            break
                        
                    composite_node = None
                    for node in node_tree.nodes:
                        if node.type == 'COMPOSITE':
                            composite_node = node
                            break
                    if glare_node and composite_node:
                        if glare_node.outputs["Image"].is_linked and composite_node.inputs["Image"].is_linked:
                            row.operator("render.clear_compositor", text="Disable", icon='RESTRICT_RENDER_ON')                            
                        else:
                            row.operator("object.post_production", text="Activate", icon='QUIT')
                    box = layout.box()
                    row = box.row(align=True)
                    row.operator("render.rest_compositor", text="Rest", icon= 'FILE_REFRESH')
                    row.operator("render.delete_compositor", text="Delete", icon= 'TRASH')

                    if glare_node and composite_node:
                        if glare_node.outputs["Image"].is_linked and composite_node.inputs["Image"].is_linked:
                            comp_nodes = context.scene.node_tree.nodes
                            for node in comp_nodes:
                                
                                Group_node = None
                                for node in node_tree.nodes:
                                    if node.label == "KH-Post Group":
                                        Group_node = node
                                        break

                                if Group_node is not None:
                                    if bpy.context.scene.use_nodes:
                                        Group_node = node
                                        box = layout.box()
                                        
                                        box.label(text="Color :", icon= 'COLOR')
                                        box.prop(Group_node.inputs[1], "default_value", text="Tint")
                                        box.prop(Group_node.inputs[2], "default_value", text="Saturation")
                                        box.prop(scene.view_settings, "use_curve_mapping", text="Color balance", toggle=True)
                                        if scene.view_settings.use_curve_mapping :
                                            box.prop(context.scene, "temperature_value", text="Temperature", slider=True)
                                            box.prop(scene.view_settings.curve_mapping,"white_level", index=0, text="Red")
                                            box.prop(scene.view_settings.curve_mapping,"white_level", index=1, text="Green")
                                            box.prop(scene.view_settings.curve_mapping,"white_level", index=2, text="Blue")

                                        box.label(text="Brightness/Contrast", icon= 'BRUSHES_ALL')                               
                                        box.prop(Group_node.inputs[3], "default_value", text="Exposure", slider=True) 
                                        box.prop(Group_node.inputs[4], "default_value", text="Brightness")      
                                        box.prop(Group_node.inputs[5], "default_value", text="Gamma")
                                        
                                        box = layout.box()
                                        box.label(text="Shadow :", icon= 'SHADING_RENDERED')
                                        box.prop(Group_node.inputs[6], "default_value", text="Brightness")
                                        box.prop(Group_node.inputs[7], "default_value", text="Contrast") 
                                        
                                        box = layout.box()
                                        box.label(text="Details :", icon= 'GRIP')
                                        box.prop(Group_node.inputs[8], "default_value", text="Sharpens") 
                                        box.prop(Group_node.inputs[9], "default_value", text="Details") 
                                        break
                                
                            
                            
                            
                            # Draw the checkbox to enable/disable mute
                            if glare1_node is not None:
                                box = layout.box() 
                                box.label(text="Fog Glow") 
                                box.prop(glare1_node, "mute", text="Fog Glow", toggle=True)
                                if not glare1_node.mute:
                                    box.prop(glare1_node, "glare_type")            
                                    box.prop(glare1_node, "quality")
                                    box.prop(glare1_node, "mix", slider=True)
                                    box.prop(glare1_node, "threshold")
                                    box.prop(glare1_node, "size")

                            if glare_node is not None:
                                box = layout.box() 
                                box.label(text="Glare Stare") 
                                box.prop(glare_node, "mute", text="Glare Stare", toggle=True)
                                if not glare_node.mute:
                                    box.prop(glare_node, "glare_type")
                                    box.prop(glare_node, "quality")
                                    box.prop(glare_node, "iterations")
                                    box.prop(glare_node,"color_modulation")
                                    box.prop(glare_node, "mix", slider=True)
                                    box.prop(glare_node, "threshold")
                                    box.prop(glare_node, "fade")
       


class Post_Productions_IMAGE_EDITOR_Panel(bpy.types.Panel):
    bl_idname = "VIEW3D_PT_post_productions_image_editor"
    bl_label = "Post Production"
    bl_space_type = 'IMAGE_EDITOR'
    bl_region_type = "UI"
    bl_category = "Light Mix"

    @classmethod
    def poll(cls, context):
        if 'KH-Tools' in context.preferences.addons:
            KH = context.preferences.addons['KH-Tools'].preferences.Light_Mix == True
            return KH
        else:
            return True
    
    def draw_header(self, context: bpy.types.Context):
        try:
            self.layout.label(
                text="", icon='SHADERFX')
        except KeyError:
            pass        

    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        if not bpy.context.scene.use_nodes == True :
            layout = self.layout
            box = layout.box()
            row = box.row(align=True)           
            row.operator("object.post_production", text="Activate")
        else:
            compositor_window = bpy.context.scene.node_tree
            render_layers = compositor_window.nodes.get("Render Layers")
            if render_layers :
                current_scene = bpy.context.scene
                windows = bpy.context.window_manager.windows
                compositing_window = None
                for window in windows:
                    screen = window.screen
                    for area in screen.areas:
                        if area.type == 'NODE_EDITOR' and area.ui_type == 'CompositorNodeTree':
                            compositing_window = window

                if not compositing_window:
                    box = layout.box()
                    row = box.row(align=True)
                    row .operator("object.compositor", icon='ERROR')
                else :
                    target_node_name = "KH-Post Group"
                    compositor = bpy.context.scene.node_tree
                    target_node = None
                    for node in compositor.nodes:
                        if node.label == target_node_name:
                            target_node = node
                            break
                    if target_node is None:
                        box = layout.box()
                        row = box.row(align=True)
                        row .operator("object.post_production", text="Activate", icon='QUIT')
                    else:
                        box = layout.box()
                        row = box.row(align=True)
                        scene = bpy.context.scene
                        node_tree = scene.node_tree
                        
                        glare_node = None
                        for node in node_tree.nodes:
                            if node.label == "KH-Post SIMPLE_STAR":
                                glare_node = node
                                break
                        
                        Group_node = None
                        for node in node_tree.nodes:
                            if node.label == "KH-Post Group":
                                Group_node = node
                                break
                            
                        glare1_node = None
                        for node in node_tree.nodes:
                            if node.label == "KH-Post FOG_GLOW":
                                glare1_node = node
                                break
                            
                        composite_node = None
                        for node in node_tree.nodes:
                            if node.type == 'COMPOSITE':
                                composite_node = node
                                break
                            
                        if glare_node and composite_node:
                            if glare_node.outputs["Image"].is_linked and composite_node.inputs["Image"].is_linked:                               
                                row.operator("render.clear_compositor", text="Disable", icon='RESTRICT_RENDER_ON')
                            else:
                                row.operator("object.post_production", text="Activate", icon='QUIT')
                        row = box.row(align=True)
                        row.operator("render.rest_compositor", text="Rest", icon= 'FILE_REFRESH')
                        row.operator("render.delete_compositor", text="Delete", icon= 'TRASH')
                        
                        box = layout.box()
                        box.prop(scene, "color_management_enabled" , icon='IMAGE_DATA', toggle=False)
                        if scene.color_management_enabled:
                            box.prop(scene.view_settings, "view_transform")
                            box.prop(scene.view_settings, "look")
                            box.prop(scene.view_settings, "exposure")
                            box.prop(scene.view_settings, "gamma")
                                                 
                            

                        if glare_node and composite_node:
                            if glare_node.outputs["Image"].is_linked and composite_node.inputs["Image"].is_linked:
                                comp_nodes = context.scene.node_tree.nodes
                                for node in comp_nodes:

                                    Group_node = None
                                    for node in node_tree.nodes:
                                        if node.label == "KH-Post Group":
                                            Group_node = node
                                            break

                                    if Group_node is not None:
                                        if bpy.context.scene.use_nodes:
                                            Group_node = node
                                            box = layout.box()
                                            
                                            box.label(text="Color :", icon= 'COLOR')
                                            box.prop(Group_node.inputs[1], "default_value", text="Tint")
                                            box.prop(Group_node.inputs[2], "default_value", text="Saturation")
                                            box.prop(scene.view_settings, "use_curve_mapping", text="Color balance", toggle=True)
                                            if scene.view_settings.use_curve_mapping :
                                                box.prop(context.scene, "temperature_value", text="Temperature", slider=True)
                                                box.prop(scene.view_settings.curve_mapping,"white_level", index=0, text="Red")
                                                box.prop(scene.view_settings.curve_mapping,"white_level", index=1, text="Green")
                                                box.prop(scene.view_settings.curve_mapping,"white_level", index=2, text="Blue")

                                            box.label(text="Brightness/Contrast", icon= 'BRUSHES_ALL')                               
                                            box.prop(Group_node.inputs[3], "default_value", text="Exposure", slider=True) 
                                            box.prop(Group_node.inputs[4], "default_value", text="Brightness")      
                                            box.prop(Group_node.inputs[5], "default_value", text="Gamma")
                                            
                                            box = layout.box()
                                            box.label(text="Shadow :", icon= 'SHADING_RENDERED')
                                            box.prop(Group_node.inputs[6], "default_value", text="Brightness")
                                            box.prop(Group_node.inputs[7], "default_value", text="Contrast") 
                                            
                                            box = layout.box()
                                            box.label(text="Details :", icon= 'GRIP')
                                            box.prop(Group_node.inputs[8], "default_value", text="Sharpens") 
                                            box.prop(Group_node.inputs[9], "default_value", text="Details") 
                                            break 
                                    
                                # Draw the checkbox to enable/disable mute
                                if glare1_node is not None:
                                    box = layout.box() 
                                    box.label(text="Fog Glow") 
                                    box.prop(glare1_node, "mute", text="Disable", icon='RESTRICT_VIEW_ON')
                                    if not glare1_node.mute:            
                                        box.prop(glare1_node, "quality")
                                        box.prop(glare1_node, "mix", slider=True)
                                        box.prop(glare1_node, "threshold")
                                        box.prop(glare1_node, "size")

                                if glare_node is not None:
                                    box = layout.box() 
                                    box.label(text="Glare Stare") 
                                    box.prop(glare_node, "mute",text="Disable", icon='RESTRICT_VIEW_ON')
                                    if not glare_node.mute:
                                        box.prop(glare_node, "glare_type")
                                        box.prop(glare_node, "quality")
                                        box.prop(glare_node, "iterations")
                                        box.prop(glare_node, "mix", slider=True)
                                        box.prop(glare_node, "threshold")
                                        box.prop(glare_node, "fade")
                                        


def update_temperature_value(self, context):
    # تحديث قيمة index=0 وindex=1 وindex=2 عند تغيير درجة الحرارة
    temperature_value = context.scene.temperature_value
    white_level = context.scene.view_settings.curve_mapping.white_level

    # حساب التناسب بناءً على الشروط المطلوبة
    if temperature_value == 6500:
        context.scene.view_settings.curve_mapping.white_level[0] = 1
        context.scene.view_settings.curve_mapping.white_level[1] = 1
        context.scene.view_settings.curve_mapping.white_level[2] = 1
        
    elif temperature_value > 6500:
        proportion = (temperature_value - 6500) / 3600
        context.scene.view_settings.curve_mapping.white_level[0] = 1 + proportion
        context.scene.view_settings.curve_mapping.white_level[1] = 1
        context.scene.view_settings.curve_mapping.white_level[2] = 1 - proportion
        
    elif temperature_value < 6500:
        proportion = (temperature_value - 6500)/ 6200
        context.scene.view_settings.curve_mapping.white_level[0] = 1 + proportion
        context.scene.view_settings.curve_mapping.white_level[1] = 1
        context.scene.view_settings.curve_mapping.white_level[2] = 1 - proportion

# إضافة خاصية درجة الحرارة إلى السيناريو
bpy.types.Scene.temperature_value = bpy.props.FloatProperty(
    name="Temperature",
    description="Adjust Temperature",
    default=6500,
    min=1800,
    max=9999,
    update=update_temperature_value
)

           

