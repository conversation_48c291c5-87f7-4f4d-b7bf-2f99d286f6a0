import bpy
from bpy.app.handlers import persistent
import  os

photographer_folder = os.path.dirname(os.path.realpath(__file__))
LGB_VERSION = 1
lightgroup_blending_grp_name = 'Light Group Blending v' + str(LGB_VERSION)

def append_node_group(group_name,retrocompatible=False):
    path = "blends/node_groups.blend/NodeTree/"
    # if retrocompatible and bpy.app.version < (3,4,0):
    #     path = "blends/node_groups_3.3.blend/NodeTree/"
    dir = os.path.join(photographer_folder,path)
    dir = os.path.normpath(dir)
    grp = None
    grps = [g for g in bpy.data.node_groups if g.name == group_name]
    if grps:
        grp = grps[0]
    if not grp:
        bpy.ops.wm.append(filename=group_name, directory=dir)
        grp = [g for g in bpy.data.node_groups if g.name == group_name][0]
    return grp
     


class open_exr_file(bpy.types.Operator):
    bl_idname = "view3d.open_exr_file"
    bl_label = "open_exr_file"
    def execute(self, context):

        target_workspace = "Compositing"
        if target_workspace in bpy.data.workspaces:
            main_window = bpy.context.window_manager.windows[0]
            main_window.workspace = bpy.data.workspaces[target_workspace]
        else :
                bpy.ops.wm.window_new()
                window = bpy.context.window_manager.windows[-1]
                bpy.context.area.ui_type = 'CompositorNodeTree'

        bpy.context.scene.use_nodes = True
        nodes = bpy.context.scene.node_tree.nodes
        if 'Render Layers' not in nodes:
            r_layers = nodes.new('CompositorNodeRLayers')
            r_layers.location = (-600, 0)
        else:
            r_layers = nodes['Render Layers']
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (200, 0)
            links = bpy.context.scene.node_tree.links
            links.new(r_layers.outputs['Image'], composite.inputs['Image'])
        else:
            composite = nodes['Composite']
        return {'FINISHED'}





class VIEW3D_OT_exrLightGroup(bpy.types.Operator):
    bl_idname = "view3d.exr_light_group"
    bl_label = "exr Light Mix"
    
    def execute(self, context):
        bpy.context.scene.use_nodes = True
        scene = context.scene
        view_layer = context.view_layer      
        if not bpy.data.scenes["Scene"].node_tree.nodes["Image"].layer:
            self.report({'WARNING'}, "This image does not support Light mix.")
            return {'CANCELLED'}
        bpy.data.scenes["Scene"].node_tree.nodes["Image"].layer = 'ViewLayer'

        nodes = bpy.context.scene.node_tree.nodes
        if 'Image'not in nodes:
            r_layers = nodes.new('CompositorNodeImage')
            r_layers.location = (-600, 0)
        else:
            r_layers = nodes['Image'] 
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (200, 0)
            links = bpy.context.scene.node_tree.links
            links.new(r_layers.outputs[0], composite.inputs['Image'])
        else:
            composite = nodes['Composite']

        composite_node_name = 'Composite'
        scene = bpy.context.scene
        composite_node = scene.node_tree.nodes.get(composite_node_name)
        if composite_node and composite_node.type == 'COMPOSITE':
            r_layers = scene.node_tree.nodes[0]
            if not composite_node.inputs['Image'].links:
                scene.node_tree.links.new(r_layers.outputs[0], composite_node.inputs['Image'])
        else:
            print(f"النود {composite_node_name} غير موجود أو ليس من نوع النود الصحيح.")

        render_layer_node_name = "Image"
        if render_layer_node_name in bpy.context.scene.node_tree.nodes:
            render_layer_node = bpy.context.scene.node_tree.nodes[render_layer_node_name]

        
        nodes = bpy.context.scene.node_tree.nodes
        if 'Composite' not in nodes:
            composite_node= nodes.new('CompositorNodeComposite')
        else:
            composite_node = nodes['Composite']
        composite_node = bpy.context.scene.node_tree.nodes.get("Composite")
        if composite_node:
            nodes = bpy.context.scene.node_tree.nodes
            if 'Image' not in nodes:
                r_layers = nodes.new('CompositorNodeImage')
            else:
                r_layers = nodes['Image']

            denoise_node = bpy.context.scene.node_tree.nodes.new("CompositorNodeDenoise")
            denoise_node.location = (-200, 0)
            denoise_node.prefilter = 'FAST'
            denoise_node.label = "Denoise Light Mix"
            #denoise_node.mute = True
            
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs[0], denoise_node.inputs[0])
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs["Denoising Normal"], denoise_node.inputs[1])
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs["Denoising Albedo"], denoise_node.inputs[2])
            bpy.context.scene.node_tree.links.new(denoise_node.outputs[0], composite_node.inputs[0])
            

        
        Switch = bpy.context.scene.node_tree.nodes.new('CompositorNodeSwitch')
        Switch.label = "Switch Light Mix"
        Switch.location = (-50, 0)
        Switch.check = True

        

        links = bpy.context.scene.node_tree.links        
        switch_label = "Switch Light Mix"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'SWITCH' and node.label == switch_label:
                switch_node = node
                break

        if switch_node:
            composite_node = bpy.context.scene.node_tree.nodes.get('Composite')
            image_input = composite_node.inputs['Image']
            connected_node = None
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == composite_node and link.to_socket == image_input:
                    connected_node = link.from_node
                    break
                
            if connected_node:
                switch_input = switch_node.inputs['On']
                bpy.context.scene.node_tree.links.new(connected_node.outputs[0], switch_input)
                print("تم ربط النود بنجاح!")
            else:
                print("لا يوجد نود متصل بمدخل 'Image' في Composite.")
        else:
            print("لا يوجد سويتش بالاسم المحدد.")
            
        links.new(r_layers.outputs[0], Switch.inputs['Off'])
        bpy.context.scene.node_tree.links.new(switch_node.outputs['Image'], composite_node.inputs["Image"])

        #return {'FINISHED'}

        scene = bpy.context.scene
        compositing_tree = scene.node_tree
        nodes = compositing_tree.nodes
        light_groups = []
        for node in nodes:
            if node.type == 'IMAGE':
                for output in node.outputs:
                    if output.name.startswith("Combined_"):
                        light_groups.append(output.name)
        for group_name in light_groups:
            bpy.ops.scene.view_layer_add_lightgroup(name=group_name)


        scene = context.scene
        if not scene.use_nodes:
            scene.use_nodes = True

        nodes = scene.node_tree.nodes
        links = scene.node_tree.links

        lightgroup_blending_grp = append_node_group(lightgroup_blending_grp_name)

        # Look for Render Layers nodes and store links from Image socket
        render_layers_nodes = {}
        for node in nodes:
            if node.type == 'IMAGE': # and node.layer == view_layer.name:
                render_layers_nodes[node] = []
                for link in links:
                    if link.from_socket == node.outputs[0]:
                        render_layers_nodes[node].append(link.to_socket)

        # If no Render Layers node, create one
        if not render_layers_nodes:
            render_layers_node = nodes.new('CompositorNodeImage')
            render_layers_nodes[render_layers_node] = []

        # Look for 'Light Groups' frame and delete it
        for n in nodes:
            if n.name == 'PG_Lightgroups_Frame':
                nodes.remove(n)

        for rln in render_layers_nodes:
            vl = rln.layer
            loc = rln.location
            lg_loc = None   

            # Create Lightgroup group nodes for as many Lightgroups in the view layer
            lgs = [lg.name for lg in scene.view_layers[vl].lightgroups]

            for i, lg in enumerate(lgs):
                lg_blending=[]

                # Check if there is already a Lightgroup node connected to the Render Layer output
                for link in links:
                    if link.from_socket == rln.outputs[lg]:
                        if link.to_node.type == 'GROUP' and link.to_node.node_tree.name == lightgroup_blending_grp_name:
                            lg_blending = link.to_node
                            lg_loc = lg_blending.location

                # If not, create a Lightgroup node
                if not lg_blending:
                    lg_blending = nodes.new('CompositorNodeGroup')
                    lg_blending.node_tree = lightgroup_blending_grp
                    lg_blending.label = lg_blending.name = lg
                    lg_blending.node_tree.use_fake_user = True
                    lg_blending.width = 170

                    # Arrange position based on already existing Lightgroup
                    if lg_loc:
                        lg_blending.location = (lg_loc[0], loc[1] + int(i)*-200)
                    # Or take Render Layer node position
                    else:
                        if i == 0:
                            rln.location[0] += -250
                            lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)
                        else:
                            lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)

                # Store Lightgroup index to reconnect them later
                lg_blending['lg_index'] = i

                # Connect first Lightgroup output reusing Render Layer node output connection
                # if i == 0:
                for link in render_layers_nodes[rln]:
                    links.new(lg_blending.outputs[0], link)

                # Connect Render Layer node to Lightgroup node
                links.new(rln.outputs[lg], lg_blending.inputs[1])

                # Connect Lightgroups together
                if i>=1:
                    for node in nodes:
                        if node.get('lg_index',-1) == i-1:
                            lg_blending.location = (node.location[0], loc[1] + int(i)*-200)
                            links.new(node.outputs[0], lg_blending.inputs[0])
                            # for link in links:
                            #     if link.from_socket == node.outputs[0]:
                            #         links.new(lg_blending.outputs[0], link.to_socket)    
                else:
                    if lg_loc:
                        lg_blending.location = (lg_loc[0], loc[1] + int(i)*-200)
                    else:
                        rln.location[0] += -250
                        lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)

        
        #bpy.context.scene.node_tree.links.new(denoise_node.outputs[0], composite_node.inputs[0])
        # Deselecting nodes
        selected_nodes = [n for n in nodes if n.select==True]
        for n in selected_nodes:
            n.select = False


        target_switch_label = "Switch Light Mix"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'SWITCH' and node.label == target_switch_label:
                switch_node = node
                break
        render_layer_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'IMAGE':
                render_layer_node = node
                break
        if switch_node:
            bpy.context.scene.node_tree.links.new(switch_node.inputs['Off'], render_layer_node.outputs[0])
        else:
            print("لا يوجد نود رندر لير في النود تري") 

        
        Viewer = bpy.context.scene.node_tree.nodes.new('CompositorNodeViewer')
        #Switch.label = "Switch Light Mix"
        Viewer.location = (0, -150)
        links.new(Viewer.inputs[0], Switch.outputs[0])

        image_name = "Viewer Node"  # قم بتغييرها إلى اسم الصورة الخاصة بك
        image = bpy.data.images.get(image_name)
        if image:
            bpy.ops.wm.window_new()
            bpy.context.area.ui_type = 'IMAGE_EDITOR'
            bpy.context.space_data.image = image
            bpy.context.space_data.show_region_ui = False
        else:
            print(f"الصورة باسم {image_name} غير موجودة.")

        return {'FINISHED'}
    

class VIEW3D_OT_Remove_exr(bpy.types.Operator):
    bl_idname = "view3d.remove_exr"
    bl_label = "Remove exr Light Mix"
    
    def execute(self, context):
        compositor_window  = bpy.context.scene.node_tree
        for node in compositor_window.nodes:
            if node.type == "COMPOSITE":
                compositor_window.nodes.remove(node)
                
        for node in compositor_window.nodes:
            if node.type == 'VIEWER':
                compositor_window.nodes.remove(node)

        bpy.ops.scene.view_layer_remove_unused_lightgroups()
        compositing_nodes = bpy.context.scene.node_tree.nodes
        nodes_to_delete = [node for node in compositing_nodes if node.name.startswith("Combined")]
        for node in nodes_to_delete:
            compositing_nodes.remove(node)


        bpy.context.scene.view_layers[0].cycles.denoising_store_passes = True
        light_groups = [lg for lg in bpy.context.scene.view_layers[0].lightgroups]
        for lg in light_groups:
            bpy.ops.scene.view_layer_remove_lightgroup()
            compositor_window = bpy.context.scene.node_tree
            Denoise_node = compositor_window.nodes.get("Switch")
            if Denoise_node:
                denoise_output_linked = False
                for link in compositor_window.links:
                    if link.from_node == Denoise_node and link.from_socket == Denoise_node.outputs[0]:
                        denoise_output_linked = True
                        denoise_output_link = link
                        break
                else:
                    print("لا يوجد ربط متصل بـ Denoise node - .outputs[0]")
            else:
                print("لا يمكن العثور على Denoise node")
        else:
            print("لا يمكن العثور على مرحلة الرندر")

        compositor_window = bpy.context.scene.node_tree
        denoise_node = None
        for node in compositor_window.nodes:
            if node.label == "Denoise Light Mix":
                denoise_node = node
                break

        if denoise_node is not None:
            compositor_window.nodes.remove(denoise_node)
            print("تم حذف النود بنجاح")
        else:
            print("لم يتم العثور على النود بالاسم المحدد")
        Switch_node = None
        for node in compositor_window.nodes:
            if node.label == "Switch Light Mix":
                Switch_node = node
                break
        if Switch_node is not None:
            compositor_window.nodes.remove(Switch_node)
            print("تم حذف النود بنجاح")
        else:
            print("لم يتم العثور على النود بالاسم المحدد")
                   
        #bpy.data.scenes["Scene"].node_tree.nodes["Render Layers"].layer = 'ViewLayer'
        # ابحث عن النودز في نافذة الكومبوزيتنغ
        for node in bpy.context.scene.node_tree.nodes:
            # افحص إذا كان النود من نوع 'IMAGE'
            if node.type == 'IMAGE':
                # احذف النود
                bpy.context.scene.node_tree.nodes.remove(node)


        self.report({'INFO'}, f"EXR Deleted")
        return {'FINISHED'}
    

class reset_exr(bpy.types.Operator):
    bl_idname = "view3d.reset_exr"
    bl_label = "Reset exr Light Mix"
    
    def execute(self, context):
        compositor_window  = bpy.context.scene.node_tree
        for node in compositor_window.nodes:
            if node.type == "COMPOSITE":
                compositor_window.nodes.remove(node)
                
        for node in compositor_window.nodes:
            if node.type == 'VIEWER':
                compositor_window.nodes.remove(node)

        bpy.ops.scene.view_layer_remove_unused_lightgroups()
        compositing_nodes = bpy.context.scene.node_tree.nodes
        nodes_to_delete = [node for node in compositing_nodes if node.name.startswith("Combined")]
        for node in nodes_to_delete:
            compositing_nodes.remove(node)


        bpy.context.scene.view_layers[0].cycles.denoising_store_passes = True
        light_groups = [lg for lg in bpy.context.scene.view_layers[0].lightgroups]
        for lg in light_groups:
            bpy.ops.scene.view_layer_remove_lightgroup()
            compositor_window = bpy.context.scene.node_tree
            Denoise_node = compositor_window.nodes.get("Switch")
            if Denoise_node:
                denoise_output_linked = False
                for link in compositor_window.links:
                    if link.from_node == Denoise_node and link.from_socket == Denoise_node.outputs[0]:
                        denoise_output_linked = True
                        denoise_output_link = link
                        break
                else:
                    print("لا يوجد ربط متصل بـ Denoise node - .outputs[0]")
            else:
                print("لا يمكن العثور على Denoise node")
        else:
            print("لا يمكن العثور على مرحلة الرندر")

        compositor_window = bpy.context.scene.node_tree
        denoise_node = None
        for node in compositor_window.nodes:
            if node.label == "Denoise Light Mix":
                denoise_node = node
                break

        if denoise_node is not None:
            compositor_window.nodes.remove(denoise_node)
            print("تم حذف النود بنجاح")
        else:
            print("لم يتم العثور على النود بالاسم المحدد")
        Switch_node = None
        for node in compositor_window.nodes:
            if node.label == "Switch Light Mix":
                Switch_node = node
                break
        if Switch_node is not None:
            compositor_window.nodes.remove(Switch_node)
            print("تم حذف النود بنجاح")
        else:
            print("لم يتم العثور على النود بالاسم المحدد")
                   
        #bpy.data.scenes["Scene"].node_tree.nodes["Render Layers"].layer = 'ViewLayer'

        self.report({'INFO'}, f"Light Mix has been Deleted")
        
        #///////////////////////////////////////////////////////////////////////////////////////////////////

        bpy.context.scene.use_nodes = True
        scene = context.scene
        view_layer = context.view_layer      
        if not bpy.data.scenes["Scene"].node_tree.nodes["Image"].layer:
            self.report({'WARNING'}, "This image does not support Light mix.")
            return {'CANCELLED'}
        bpy.data.scenes["Scene"].node_tree.nodes["Image"].layer = 'ViewLayer'

        nodes = bpy.context.scene.node_tree.nodes
        if 'Image'not in nodes:
            r_layers = nodes.new('CompositorNodeImage')
            r_layers.location = (-600, 0)
        else:
            r_layers = nodes['Image'] 
        if 'Composite' not in nodes:
            composite = nodes.new('CompositorNodeComposite')
            composite.location = (200, 0)
            links = bpy.context.scene.node_tree.links
            links.new(r_layers.outputs[0], composite.inputs['Image'])
        else:
            composite = nodes['Composite']

        composite_node_name = 'Composite'
        scene = bpy.context.scene
        composite_node = scene.node_tree.nodes.get(composite_node_name)
        if composite_node and composite_node.type == 'COMPOSITE':
            r_layers = scene.node_tree.nodes[0]
            if not composite_node.inputs['Image'].links:
                scene.node_tree.links.new(r_layers.outputs[0], composite_node.inputs['Image'])
        else:
            print(f"النود {composite_node_name} غير موجود أو ليس من نوع النود الصحيح.")

        render_layer_node_name = "Image"
        if render_layer_node_name in bpy.context.scene.node_tree.nodes:
            render_layer_node = bpy.context.scene.node_tree.nodes[render_layer_node_name]

        
        nodes = bpy.context.scene.node_tree.nodes
        if 'Composite' not in nodes:
            composite_node= nodes.new('CompositorNodeComposite')
        else:
            composite_node = nodes['Composite']
        composite_node = bpy.context.scene.node_tree.nodes.get("Composite")
        if composite_node:
            nodes = bpy.context.scene.node_tree.nodes
            if 'Image' not in nodes:
                r_layers = nodes.new('CompositorNodeImage')
            else:
                r_layers = nodes['Image']

            denoise_node = bpy.context.scene.node_tree.nodes.new("CompositorNodeDenoise")
            denoise_node.location = (-200, 0)
            denoise_node.prefilter = 'FAST'
            denoise_node.label = "Denoise Light Mix"
            #denoise_node.mute = True
            
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs[0], denoise_node.inputs[0])
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs["Denoising Normal"], denoise_node.inputs[1])
            bpy.context.scene.node_tree.links.new(render_layer_node.outputs["Denoising Albedo"], denoise_node.inputs[2])
            bpy.context.scene.node_tree.links.new(denoise_node.outputs[0], composite_node.inputs[0])
            

        
        Switch = bpy.context.scene.node_tree.nodes.new('CompositorNodeSwitch')
        Switch.label = "Switch Light Mix"
        Switch.location = (-50, 0)
        Switch.check = True

        

        links = bpy.context.scene.node_tree.links        
        switch_label = "Switch Light Mix"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'SWITCH' and node.label == switch_label:
                switch_node = node
                break

        if switch_node:
            composite_node = bpy.context.scene.node_tree.nodes.get('Composite')
            image_input = composite_node.inputs['Image']
            connected_node = None
            for link in bpy.context.scene.node_tree.links:
                if link.to_node == composite_node and link.to_socket == image_input:
                    connected_node = link.from_node
                    break
                
            if connected_node:
                switch_input = switch_node.inputs['On']
                bpy.context.scene.node_tree.links.new(connected_node.outputs[0], switch_input)
                print("تم ربط النود بنجاح!")
            else:
                print("لا يوجد نود متصل بمدخل 'Image' في Composite.")
        else:
            print("لا يوجد سويتش بالاسم المحدد.")
            
        links.new(r_layers.outputs[0], Switch.inputs['Off'])
        bpy.context.scene.node_tree.links.new(switch_node.outputs['Image'], composite_node.inputs["Image"])

        #return {'FINISHED'}

        scene = bpy.context.scene
        compositing_tree = scene.node_tree
        nodes = compositing_tree.nodes
        light_groups = []
        for node in nodes:
            if node.type == 'IMAGE':
                for output in node.outputs:
                    if output.name.startswith("Combined_"):
                        light_groups.append(output.name)
        for group_name in light_groups:
            bpy.ops.scene.view_layer_add_lightgroup(name=group_name)


        scene = context.scene
        if not scene.use_nodes:
            scene.use_nodes = True

        nodes = scene.node_tree.nodes
        links = scene.node_tree.links

        lightgroup_blending_grp = append_node_group(lightgroup_blending_grp_name)

        # Look for Render Layers nodes and store links from Image socket
        render_layers_nodes = {}
        for node in nodes:
            if node.type == 'IMAGE': # and node.layer == view_layer.name:
                render_layers_nodes[node] = []
                for link in links:
                    if link.from_socket == node.outputs[0]:
                        render_layers_nodes[node].append(link.to_socket)

        # If no Render Layers node, create one
        if not render_layers_nodes:
            render_layers_node = nodes.new('CompositorNodeImage')
            render_layers_nodes[render_layers_node] = []

        # Look for 'Light Groups' frame and delete it
        for n in nodes:
            if n.name == 'PG_Lightgroups_Frame':
                nodes.remove(n)

        for rln in render_layers_nodes:
            vl = rln.layer
            loc = rln.location
            lg_loc = None   

            # Create Lightgroup group nodes for as many Lightgroups in the view layer
            lgs = [lg.name for lg in scene.view_layers[vl].lightgroups]

            for i, lg in enumerate(lgs):
                lg_blending=[]

                # Check if there is already a Lightgroup node connected to the Render Layer output
                for link in links:
                    if link.from_socket == rln.outputs[lg]:
                        if link.to_node.type == 'GROUP' and link.to_node.node_tree.name == lightgroup_blending_grp_name:
                            lg_blending = link.to_node
                            lg_loc = lg_blending.location

                # If not, create a Lightgroup node
                if not lg_blending:
                    lg_blending = nodes.new('CompositorNodeGroup')
                    lg_blending.node_tree = lightgroup_blending_grp
                    lg_blending.label = lg_blending.name = lg
                    lg_blending.node_tree.use_fake_user = True
                    lg_blending.width = 170

                    # Arrange position based on already existing Lightgroup
                    if lg_loc:
                        lg_blending.location = (lg_loc[0], loc[1] + int(i)*-200)
                    # Or take Render Layer node position
                    else:
                        if i == 0:
                            rln.location[0] += -250
                            lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)
                        else:
                            lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)

                # Store Lightgroup index to reconnect them later
                lg_blending['lg_index'] = i

                # Connect first Lightgroup output reusing Render Layer node output connection
                # if i == 0:
                for link in render_layers_nodes[rln]:
                    links.new(lg_blending.outputs[0], link)

                # Connect Render Layer node to Lightgroup node
                links.new(rln.outputs[lg], lg_blending.inputs[1])

                # Connect Lightgroups together
                if i>=1:
                    for node in nodes:
                        if node.get('lg_index',-1) == i-1:
                            lg_blending.location = (node.location[0], loc[1] + int(i)*-200)
                            links.new(node.outputs[0], lg_blending.inputs[0])
                            # for link in links:
                            #     if link.from_socket == node.outputs[0]:
                            #         links.new(lg_blending.outputs[0], link.to_socket)    
                else:
                    if lg_loc:
                        lg_blending.location = (lg_loc[0], loc[1] + int(i)*-200)
                    else:
                        rln.location[0] += -250
                        lg_blending.location = (loc[0]+300, loc[1] + int(i)*-200)

        
        #bpy.context.scene.node_tree.links.new(denoise_node.outputs[0], composite_node.inputs[0])
        # Deselecting nodes
        selected_nodes = [n for n in nodes if n.select==True]
        for n in selected_nodes:
            n.select = False


        target_switch_label = "Switch Light Mix"
        switch_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'SWITCH' and node.label == target_switch_label:
                switch_node = node
                break
        render_layer_node = None
        for node in bpy.context.scene.node_tree.nodes:
            if node.type == 'IMAGE':
                render_layer_node = node
                break
        if switch_node:
            bpy.context.scene.node_tree.links.new(switch_node.inputs['Off'], render_layer_node.outputs[0])
        else:
            print("لا يوجد نود رندر لير في النود تري") 

        
        Viewer = bpy.context.scene.node_tree.nodes.new('CompositorNodeViewer')
        #Switch.label = "Switch Light Mix"
        Viewer.location = (0, -150)
        links.new(Viewer.inputs[0], Switch.outputs[0])
        return {'FINISHED'}

        
