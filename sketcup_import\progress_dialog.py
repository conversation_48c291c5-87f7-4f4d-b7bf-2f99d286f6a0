# -*- coding: utf-8 -*-
"""
نافذة Progress Bar لاستيراد ملفات SketchUp
"""

import bpy
from bpy.props import StringProperty, FloatProperty, BoolProperty

class SketchUpProgressDialog(bpy.types.Operator):
    """نافذة تقدم استيراد ملف SketchUp مع progress bar حقيقي"""
    bl_idname = "import_scene.skp_progress"
    bl_label = "استيراد ملف SketchUp"
    bl_options = {'REGISTER', 'INTERNAL'}

    # خصائص التقدم
    progress: FloatProperty(
        name="التقدم",
        description="نسبة التقدم في الاستيراد",
        default=0.0,
        min=0.0,
        max=100.0,
        subtype='PERCENTAGE'
    )

    status_message: StringProperty(
        name="الحالة",
        description="رسالة الحالة الحالية",
        default="جاري التحضير للاستيراد..."
    )

    def execute(self, context):
        """تنفيذ العملية"""
        return {'FINISHED'}

    def invoke(self, context, event):
        """بدء النافذة"""
        return context.window_manager.invoke_popup(self, width=450)

    def draw(self, context):
        """رسم واجهة النافذة"""
        layout = self.layout

        # عنوان النافذة
        layout.label(text="استيراد ملف SketchUp", icon='IMPORT')
        layout.separator()

        # شريط التقدم
        col = layout.column()
        col.prop(self, "progress", text="التقدم", slider=True)

        # رسالة الحالة
        col.label(text=f"الحالة: {self.status_message}")

        # معلومات إضافية
        box = col.box()
        box.label(text="معلومات:")
        box.label(text="• تحقق من Console للتفاصيل")
        box.label(text="• العملية قد تستغرق وقتاً حسب حجم الملف")

        layout.separator()

    def update_progress(self, progress, message):
        """تحديث التقدم والرسالة"""
        self.progress = progress
        self.status_message = message
    


class CopyErrorOperator(bpy.types.Operator):
    """نسخ رسالة الخطأ إلى الحافظة"""
    bl_idname = "import_scene.copy_error"
    bl_label = "نسخ رسالة الخطأ"

    def execute(self, context):
        # نسخ رسالة الخطأ إلى الحافظة
        error_message = getattr(context.window_manager, 'skp_error_message', '')

        if error_message:
            context.window_manager.clipboard = error_message
            self.report({'INFO'}, "تم نسخ رسالة الخطأ إلى الحافظة")
        else:
            self.report({'WARNING'}, "لا توجد رسالة خطأ للنسخ")

        return {'FINISHED'}


# تسجيل الكلاسات
classes = (
    SketchUpProgressDialog,
    CopyErrorOperator,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in classes:
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
