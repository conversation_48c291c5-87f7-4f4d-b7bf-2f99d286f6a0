# -*- coding: utf-8 -*-
"""
نافذة Progress Bar لاستيراد ملفات SketchUp
"""

import bpy
import threading
import time
from bpy.props import StringProperty, FloatProperty, BoolProperty

class SketchUpProgressDialog(bpy.types.Operator):
    """نافذة تقدم استيراد ملف SketchUp مع إمكانية الإلغاء"""
    bl_idname = "import_scene.skp_progress"
    bl_label = "استيراد ملف SketchUp"
    bl_options = {'REGISTER', 'INTERNAL'}

    # خصائص النافذة
    progress: FloatProperty(
        name="التقدم",
        description="نسبة التقدم في الاستيراد",
        default=0.0,
        min=0.0,
        max=100.0,
        subtype='PERCENTAGE'
    )

    status_message: StringProperty(
        name="الحالة",
        description="رسالة الحالة الحالية",
        default="جاري التحضير للاستيراد..."
    )

    error_message: StringProperty(
        name="رسالة الخطأ",
        description="رسالة الخطأ إن وجدت",
        default=""
    )

    is_cancelled: BoolProperty(
        name="تم الإلغاء",
        description="هل تم إلغاء العملية",
        default=False
    )

    is_finished: BoolProperty(
        name="انتهت العملية",
        description="هل انتهت العملية",
        default=False
    )

    # متغيرات للـ threading
    import_thread = None
    timer = None
    width = 400
    
    def __init__(self):
        self.progress = 0.0
        self.status_message = "جاري التحضير للاستيراد..."
        self.error_message = ""
        self.is_cancelled = False
        self.is_finished = False
        self.import_thread = None
        self.timer = None
    
    def modal(self, context, event):
        """معالج الأحداث للنافذة المودال"""
        
        # التحقق من إلغاء العملية
        if event.type == 'ESC' or self.is_cancelled:
            self.cancel_import()
            return {'CANCELLED'}
        
        # التحقق من انتهاء العملية
        if self.is_finished:
            if self.timer:
                context.window_manager.event_timer_remove(self.timer)
            
            if self.error_message:
                self.report({'ERROR'}, f"خطأ في الاستيراد: {self.error_message}")
                return {'CANCELLED'}
            else:
                self.report({'INFO'}, "تم استيراد الملف بنجاح")
                return {'FINISHED'}
        
        # تحديث النافذة
        if event.type == 'TIMER':
            context.area.tag_redraw()
        
        return {'PASS_THROUGH'}
    
    def invoke(self, context, event):
        """بدء النافذة كـ popup"""

        # إعداد النافذة كـ popup
        wm = context.window_manager
        return wm.invoke_popup(self, width=self.width)

    def execute(self, context):
        """تنفيذ العملية"""
        return {'FINISHED'}
    
    def draw(self, context):
        """رسم واجهة النافذة"""
        layout = self.layout
        
        # عنوان النافذة
        layout.label(text="استيراد ملف SketchUp", icon='IMPORT')
        layout.separator()
        
        # شريط التقدم
        col = layout.column()
        col.prop(self, "progress", text="التقدم", slider=True)
        
        # رسالة الحالة
        col.label(text=f"الحالة: {self.status_message}")
        
        # رسالة الخطأ إن وجدت
        if self.error_message:
            box = col.box()
            box.alert = True
            box.label(text="خطأ:", icon='ERROR')
            
            # تقسيم رسالة الخطأ إلى أسطر متعددة
            error_lines = self.error_message.split('\n')
            for line in error_lines:
                if line.strip():
                    box.label(text=line)
            
            # زر نسخ رسالة الخطأ
            box.operator("import_scene.copy_error", text="نسخ رسالة الخطأ", icon='COPYDOWN')
        
        layout.separator()
        
        # أزرار التحكم
        row = layout.row()
        if not self.is_finished:
            row.operator("import_scene.cancel_import", text="إلغاء", icon='CANCEL')
        else:
            row.operator("import_scene.close_dialog", text="إغلاق", icon='X')
    
    def cancel_import(self):
        """إلغاء عملية الاستيراد"""
        self.is_cancelled = True
        self.status_message = "جاري إلغاء العملية..."
        
        # إيقاف الـ thread إذا كان يعمل
        if self.import_thread and self.import_thread.is_alive():
            # تعيين علامة الإلغاء للـ thread
            if hasattr(self.import_thread, 'cancel_flag'):
                self.import_thread.cancel_flag = True
    
    def update_progress(self, progress, message):
        """تحديث التقدم والرسالة"""
        self.progress = progress
        self.status_message = message
    
    def set_error(self, error_message):
        """تعيين رسالة خطأ"""
        self.error_message = error_message
        self.is_finished = True
    
    def set_finished(self):
        """تعيين انتهاء العملية بنجاح"""
        self.progress = 100.0
        self.status_message = "تم الاستيراد بنجاح"
        self.is_finished = True


class CancelImportOperator(bpy.types.Operator):
    """إلغاء عملية الاستيراد"""
    bl_idname = "import_scene.cancel_import"
    bl_label = "إلغاء الاستيراد"
    
    def execute(self, context):
        # البحث عن نافذة التقدم النشطة
        for area in context.screen.areas:
            if area.type == 'INFO':
                for region in area.regions:
                    if region.type == 'WINDOW':
                        # إرسال إشارة الإلغاء
                        bpy.ops.import_scene.skp_progress('INVOKE_DEFAULT')
        
        return {'FINISHED'}


class CopyErrorOperator(bpy.types.Operator):
    """نسخ رسالة الخطأ إلى الحافظة"""
    bl_idname = "import_scene.copy_error"
    bl_label = "نسخ رسالة الخطأ"
    
    def execute(self, context):
        # البحث عن نافذة التقدم للحصول على رسالة الخطأ
        error_message = getattr(context.window_manager, 'skp_error_message', '')
        
        if error_message:
            # نسخ إلى الحافظة
            context.window_manager.clipboard = error_message
            self.report({'INFO'}, "تم نسخ رسالة الخطأ إلى الحافظة")
        else:
            self.report({'WARNING'}, "لا توجد رسالة خطأ للنسخ")
        
        return {'FINISHED'}


class CloseDialogOperator(bpy.types.Operator):
    """إغلاق نافذة التقدم"""
    bl_idname = "import_scene.close_dialog"
    bl_label = "إغلاق النافذة"
    
    def execute(self, context):
        return {'FINISHED'}


# تسجيل الكلاسات
classes = (
    SketchUpProgressDialog,
    CancelImportOperator,
    CopyErrorOperator,
    CloseDialogOperator,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in classes:
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
